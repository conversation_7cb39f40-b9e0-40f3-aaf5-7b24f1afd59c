package com.vbs.capsAllocation.service;

import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.model.Employee;
import com.vbs.capsAllocation.model.VunnoResponse;
import com.vbs.capsAllocation.repository.EmployeeRepository;
import com.vbs.capsAllocation.repository.LeaveUsageLogRepository;
import com.vbs.capsAllocation.util.EmailTemplateUtil;
import jakarta.mail.MessagingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@Service
public class NotificationService {

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailTemplateUtil emailTemplateUtil;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private LeaveUsageLogRepository leaveUsageLogRepository;

    private static final String ACCOUNT_MANAGER_EMAIL = "<EMAIL>";

    public void triggerRequestNotification(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts) {
        try {
            String ldap = requestDto.getLdap();
            String role = requestDto.getRole(); // USER, LEAD, MANAGER from frontend
            String managerLdap = dto.getManager(); // Program manager or reporting manager
            String DOMAIN = "@google.com";

            System.out.println("ROLE AND MANAGER: " + role + "  " + managerLdap);

            // Check if WFH request is more than 3 days
            boolean isWFHMoreThan3Days = isWFHMoreThan3Days(requestDto);

            // Generate email body with disclaimer if needed
            String emailBody = emailTemplateUtil.getVunnoNotificationEmail(requestDto, dto, counts, isWFHMoreThan3Days);

            // Build recipient lists
            Set<String> ccList = new HashSet<>();
            String to;

            // Route to account manager if WFH > 3 days, otherwise to regular manager
            if (isWFHMoreThan3Days) {
                to = ACCOUNT_MANAGER_EMAIL;
                System.out.println("WFH request > 3 days, routing to account manager: " + ACCOUNT_MANAGER_EMAIL);
            } else {
                to = managerLdap + DOMAIN;
            }
            ccList.add("<EMAIL>");
            if ("USER".equalsIgnoreCase(role)) {
                // TO: manager
                // CC: requestor + all leads under the same manager
                ccList.add(ldap + DOMAIN); // add requestor

                List<Employee> underManager = employeeRepository.findByProgramManager(managerLdap);
                for (Employee emp : underManager) {
                    if ("Team Lead".equalsIgnoreCase(emp.getLevel()) && emp.getLdap() != null) {
                        ccList.add(emp.getLdap() + DOMAIN);
                    }
                }
            } else if ("LEAD".equalsIgnoreCase(role) || "MANAGER".equalsIgnoreCase(role)) {
                // TO: manager
                // CC: only the requestor
                ccList.add(ldap + DOMAIN); // add requestor only
            }

            // Debug CC list
            for (String cc : ccList) {
                System.out.println("List of People in CC: " + cc);
            }

            // Email subject - include leave type for leave requests
            String subjectPrefix = isWFHMoreThan3Days ? "Teamsphere [ACCOUNT MANAGER] " : "Teamsphere ";
            String leaveTypeInfo = "";
            if ("Leave".equalsIgnoreCase(requestDto.getApplicationType()) && requestDto.getLeaveType() != null) {
                leaveTypeInfo = " (" + requestDto.getLeaveType() + ")";
            }
            String subject = String.format("%s%s Request%s | %s | %s - %s | %s",
                    subjectPrefix,
                    requestDto.getApplicationType(),
                    leaveTypeInfo,
                    ldap,
                    requestDto.getStartDate(),
                    requestDto.getEndDate(),
                    requestDto.getLvWfhDuration()
            );

            // Send email ccList
            emailService.sendEmail(
                    List.of(to),
                    new ArrayList<>(ccList),
                    subject,
                    emailBody
            );

        } catch (Exception e) {
            System.err.println("Error sending approval notification email: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send approval notification email", e);
        }
    }

    public void triggerApprovalNotification(VunnoRequestDto requestDto, VunnoResponse response, String role, VunnoMgmtDto dto) {
        try {
            String DOMAIN = "@google.com";
            String ldap = requestDto.getLdap();           // Requestor
            String approverLdap = response.getApprover(); // Approver
            String managerLdap = dto.getManager();
            System.out.println("ROLE AND MANAGER: " + role + "  " + managerLdap);

            // Build recipient lists
            Set<String> ccList = new HashSet<>();
            String to = managerLdap + DOMAIN; // Final TO address - vf-gur-vbs

            if ("USER".equalsIgnoreCase(role)) {
                // TO: vf-gur-vbs
                // CC: requestor + all leads under the same manager
                ccList.add(ldap + DOMAIN);

                List<Employee> underManager = employeeRepository.findByProgramManager(managerLdap);
                for (Employee emp : underManager) {
                    if ("Team Lead".equalsIgnoreCase(emp.getLevel()) && emp.getLdap() != null) {
                        ccList.add(emp.getLdap() + DOMAIN);
                    }
                }
            } else if ("LEAD".equalsIgnoreCase(role) || "MANAGER".equalsIgnoreCase(role)) {
                // TO: vf-gur-vbs
                // CC: requestor + manager only
                ccList.add(ldap + DOMAIN);
                if (managerLdap != null && !managerLdap.equalsIgnoreCase(ldap)) {
                    ccList.add(managerLdap + DOMAIN);
                }
            }

            // Email subject
            String subject = String.format("Teamsphere [REQUEST APPROVED] | %s | %s | %s - %s",
                    ldap,
                    response.getApplicationType(),
                    response.getFromDate(),
                    response.getToDate()
            );

            System.out.println("Request " + requestDto.getApplicationType() + " Response " + response.getApplicationType());

            // Email body
            String emailBody = emailTemplateUtil.getVunnoApprovalEmail(
                    response.getBackup(),
                    response.getApplicationType(),
                    response.getFromDate().toString(),
                    response.getToDate().toString(),
                    response.getDuration()
            );

            // Debug logging
            System.out.println("Sending approval mail TO: " + to);
            for (String cc : ccList) {
                System.out.println("CC: " + cc);
            }
            // Send email ccList
           emailService.sendEmail(
                   List.of(to),
                   new ArrayList<>(ccList),
                   subject,
                   emailBody
           );

        } catch (Exception e) {
            System.err.println("Error sending approval notification email: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send approval notification email", e);
        }
    }
    
    /**
     * Calculate the number of days for a WFH request based on duration and dates
     * @param requestDto The WFH request DTO
     * @return The number of days for the WFH request
     */
    public double calculateWFHDays(VunnoRequestDto requestDto) {
        String duration = requestDto.getLvWfhDuration();
        if (duration == null) {
            throw new RuntimeException("Duration is required for WFH calculation.");
        }

        duration = duration.trim();

        switch (duration) {
            case "Full Day":
                return 1.0;
            case "Half Day AM":
            case "Half Day PM":
                return 0.5;
            case "Multiple Days":
                if (requestDto.getStartDate() == null || requestDto.getEndDate() == null) {
                    throw new RuntimeException("Start date and end date are required for Multiple Days WFH.");
                }
                try {
                    LocalDate startDate = LocalDate.parse(requestDto.getStartDate());
                    LocalDate endDate = LocalDate.parse(requestDto.getEndDate());
                    long days = ChronoUnit.DAYS.between(startDate, endDate) + 1; // Inclusive
                    return (double) days;
                } catch (Exception e) {
                    throw new RuntimeException("Invalid date format for WFH calculation: " + e.getMessage());
                }
            default:
                throw new RuntimeException("Invalid WFH duration: " + duration);
        }
    }

    /**
     * Check if a WFH request exceeds 3 days (strictly greater than 3)
     * @param requestDto The WFH request DTO
     * @return true if the request is for more than 3 days, false otherwise
     */
    public boolean isWFHMoreThan3Days(VunnoRequestDto requestDto) {
        if (!"Work From Home".equalsIgnoreCase(requestDto.getApplicationType())) {
            return false;
        }

        try {
            double days = calculateWFHDays(requestDto);
            // Changed to strictly greater than 3 (so 3 days exactly will NOT trigger account manager email)
            return days > 3.0;
        } catch (Exception e) {
            System.err.println("Error calculating WFH days for validation: " + e.getMessage());
            return false;
        }
    }

}
