package com.vbs.capsAllocation.service;

import com.vbs.capsAllocation.dto.AttendanceRequest;
import com.vbs.capsAllocation.dto.AttendanceResponseDto;
import com.vbs.capsAllocation.dto.CheckInStatusResponse;
import com.vbs.capsAllocation.model.Attendance;
import com.vbs.capsAllocation.model.Employee;
import com.vbs.capsAllocation.model.Role;
import com.vbs.capsAllocation.model.User;
import com.vbs.capsAllocation.repository.AttendanceRepository;
import com.vbs.capsAllocation.repository.EmployeeRepository;
import com.vbs.capsAllocation.util.GeoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import com.vbs.capsAllocation.repository.UserRepository;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class AttendanceService {

    @Autowired
    private GeoUtil geoUtil;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AttendanceRepository attendanceRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    private static final DateTimeFormatter TIME_FORMATTER =
            DateTimeFormatter.ofPattern("HH:mm");

    private static final ZoneId INDIA_TIMEZONE = ZoneId.of("Asia/Kolkata");

    private static final Logger logger = LoggerFactory.getLogger(AttendanceService.class);

    public List<AttendanceResponseDto> getAttendanceRecords(String ldap, LocalDate startDate, LocalDate endDate, boolean isFilter) {
        User user = userRepository.findByUsername(ldap)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found"));
        Role role = user.getRole();

        if (startDate != null && endDate == null) {
            endDate = startDate;
        } else if (startDate == null && endDate != null) {
            startDate = endDate;
        }

        List<Attendance> records;

        if (role == Role.LEAD || role == Role.MANAGER || role == Role.ADMIN_OPS_MANAGER) {
            if (!isFilter) {
                // default to only today
                startDate = endDate = ZonedDateTime.now(INDIA_TIMEZONE).toLocalDate();
            }

            List<String> teamLdaps = switch (role) {
                case LEAD -> employeeRepository.findLdapsByLead(ldap);
                case MANAGER -> employeeRepository.findLdapsByManager(ldap);
                case ADMIN_OPS_MANAGER -> employeeRepository.findAllLdaps();
                default -> List.of();
            };

            records = attendanceRepository.findByLdapsAndDateRange(teamLdaps, startDate, endDate);
        } else {
            if (!isFilter) {
                // User: past 7 days
                endDate = ZonedDateTime.now(INDIA_TIMEZONE).toLocalDate();
                startDate = endDate.minusDays(6);
            }

            records = attendanceRepository.findByEmployeeLdapAndEntryDateBetween(ldap, startDate, endDate);
        }

        return records.stream()
                .map(this::convertToDTO)
                .toList();
    }

    private AttendanceResponseDto convertToDTO(Attendance attendance) {
        Employee emp = attendance.getEmployee();
        String name = emp.getFirstName() + " " + emp.getLastName();

        return new AttendanceResponseDto(
                attendance.getId(),
                emp.getLdap(),
                emp.getPnseProgram(),
                name,
                attendance.getEntryDate(),
                attendance.getEntryTimestamp(),
                attendance.getLateLoginReason(),
                attendance.getIsOutsideOffice(),
                attendance.getIsDefaulter(),
                attendance.getComment()
        );
    }



    public Attendance markAttendance(AttendanceRequest request) {
        // 1. Validate and fetch employee
        String ldap = request.getLdap();
        Employee employee = employeeRepository.findByLdap(ldap)
                .orElseThrow(() -> new RuntimeException("Employee not found for LDAP: " + ldap));

        // 2. Get current date and time in India timezone
        ZonedDateTime nowInIndia = ZonedDateTime.now(INDIA_TIMEZONE);
        LocalDate today = nowInIndia.toLocalDate();
        LocalDateTime checkInTime = nowInIndia.toLocalDateTime();

        // 3. Validate location and time
        boolean isInside = isUserInsideOffice(
                request.getLatitude(),
                request.getLongitude()
        );

        boolean isLate = isLateCheckIn(checkInTime);
        boolean isDefaulter = isLate || !isInside;

        // 5. Create and save attendance record
        Attendance entry = new Attendance();
        entry.setEmployee(employee); // Setting the relationship
        entry.setEntryDate(today);
        entry.setEntryTimestamp(checkInTime);
        entry.setLateLoginReason(request.getReason());
        entry.setIsOutsideOffice(!isInside);
        entry.setIsDefaulter(isDefaulter);
        entry.setComment(request.getComment());

        attendanceRepository.save(entry);

        return entry;
    }

    // Helper Methods
    private String generateAttendanceId(String ldap, LocalDate date) {
        return "ATT-" + ldap + "-" + date.toString().replace("-", "");
    }

    private boolean isLateCheckIn(LocalDateTime checkInTime) {
        return checkInTime.toLocalTime().isAfter(LocalTime.of(8, 0)); // After 8 AM
    }

    public boolean isUserInsideOffice(double userLat, double userLng) {
        List<double[]> officePolygon = geoUtil.getOfficePolygon();
        return GeoUtil.isPointInsidePolygon(userLat, userLng, officePolygon);
    }

    public String checkAttendanceStatus(String ldap) {
        LocalDate today = ZonedDateTime.now(INDIA_TIMEZONE).toLocalDate();
        String attendanceId = "ATT-" + ldap.toUpperCase() + "-" + today.toString().replace("-", "");
        if (attendanceRepository.existsById(Long.valueOf(attendanceId))) {
            return "Attendance already marked for today.";
        }
        String checkedInTime = attendanceRepository.findCheckInTimeById(attendanceId);
        return checkedInTime;
    }

    public CheckInStatusResponse getCheckInStatus(String ldap) {
        logger.debug("Getting check-in status for LDAP: {}", ldap);
        LocalDate today = ZonedDateTime.now(INDIA_TIMEZONE).toLocalDate();
        logger.debug("Querying attendance for LDAP: {} on date: {}", ldap, today);
        
        return attendanceRepository
                .findTopByEmployeeLdapAndEntryDateOrderByEntryTimestampDesc(ldap, today)
                .map(attendance -> {
                    logger.debug("Found attendance record for LDAP: {}, ID: {}, Timestamp: {}", 
                               ldap, attendance.getId(), attendance.getEntryTimestamp());
                    return this.buildCheckedInResponse(attendance);
                })
                .orElseGet(() -> {
                    logger.debug("No attendance record found for LDAP: {} on date: {}", ldap, today);
                    return this.buildNotCheckedInResponse();
                });
    }

    private CheckInStatusResponse buildCheckedInResponse(Attendance attendance) {
        LocalTime checkInTime = attendance.getEntryTimestamp().toLocalTime();
        return new CheckInStatusResponse(
                "Checked in at " + checkInTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                true,
                checkInTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                checkInTime.isAfter(LocalTime.of(8, 0)) // Late if after 8:00 AM
        );
    }

    private CheckInStatusResponse buildNotCheckedInResponse() {
        return new CheckInStatusResponse(
                "Not checked in today",
                false,
                null,
                false
        );
    }


}